cmake_minimum_required(VERSION 3.10)
project(GeekPlusAMRController)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(CURL REQUIRED libcurl)
find_package(nlohmann_json REQUIRED)

# Include directories
include_directories(${CURL_INCLUDE_DIRS})
include_directories(include)

# Library source files (excluding main.cpp)
set(LIB_SOURCES
    src/AMRController.cpp
    src/HttpClient.cpp
    src/DataStructures.cpp
)

# Main program source files
set(MAIN_SOURCES
    src/main.cpp
)

# Test source files
set(TEST_SOURCES
    test/test_amr_controller.cpp
)

# Create main executable
add_executable(${PROJECT_NAME} ${MAIN_SOURCES} ${LIB_SOURCES})

# Create test executable
add_executable(${PROJECT_NAME}_test ${TEST_SOURCES} ${LIB_SOURCES})

# Link libraries for main program
target_link_libraries(${PROJECT_NAME}
    ${CURL_LIBRARIES}
    nlohmann_json::nlohmann_json
)

# Link libraries for test program
target_link_libraries(${PROJECT_NAME}_test
    ${CURL_LIBRARIES}
    nlohmann_json::nlohmann_json
)

# Compiler flags
target_compile_options(${PROJECT_NAME} PRIVATE ${CURL_CFLAGS_OTHER})
target_compile_options(${PROJECT_NAME}_test PRIVATE ${CURL_CFLAGS_OTHER})
