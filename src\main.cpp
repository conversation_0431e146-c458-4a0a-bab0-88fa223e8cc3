#include "AMRController.h"
#include <iostream>
#include <string>
#include <sstream>
#include <iomanip>

using namespace GeekPlus;

class AMRControllerUI {
public:
    AMRControllerUI(const std::string& server_ip, int port = 8858) 
        : controller_(server_ip, port) {}
    
    void run() {
        std::cout << "=== 迦智AMR机器人控制系统 ===" << std::endl;
        std::cout << "输入 'help' 查看可用命令" << std::endl;
        
        std::string input;
        while (true) {
            std::cout << "\n> ";
            std::getline(std::cin, input);
            
            if (input == "quit" || input == "exit") {
                break;
            }
            
            processCommand(input);
        }
    }

private:
    AMRController controller_;
    
    void processCommand(const std::string& input) {
        std::istringstream iss(input);
        std::string command;
        iss >> command;
        
        if (command == "help") {
            showHelp();
        } else if (command == "robots") {
            listRobots();
        } else if (command == "robot") {
            std::string robot_id;
            iss >> robot_id;
            showRobotInfo(robot_id);
        } else if (command == "move") {
            std::string robot_id;
            double x, y;
            iss >> robot_id >> x >> y;
            moveRobot(robot_id, x, y);
        } else if (command == "rotate") {
            std::string robot_id;
            double theta;
            iss >> robot_id >> theta;
            rotateRobot(robot_id, theta);
        } else if (command == "goto") {
            std::string robot_id;
            double x, y, theta;
            iss >> robot_id >> x >> y >> theta;
            gotoPosition(robot_id, x, y, theta);
        } else if (command == "stop") {
            std::string robot_id;
            iss >> robot_id;
            stopRobot(robot_id);
        } else if (command == "missions") {
            listMissions();
        } else if (command == "cancel") {
            std::string mission_id;
            iss >> mission_id;
            cancelMission(mission_id);
        } else if (command == "maps") {
            listMaps();
        } else if (command == "map") {
            std::string map_name;
            iss >> map_name;
            showMapInfo(map_name);
        } else if (command.empty()) {
            // 空命令，忽略
        } else {
            std::cout << "未知命令: " << command << std::endl;
            std::cout << "输入 'help' 查看可用命令" << std::endl;
        }
    }
    
    void showHelp() {
        std::cout << "\n可用命令:" << std::endl;
        std::cout << "  help                     - 显示帮助信息" << std::endl;
        std::cout << "  robots                   - 列出所有机器人" << std::endl;
        std::cout << "  robot <id>               - 显示指定机器人信息" << std::endl;
        std::cout << "  move <robot_id> <x> <y>  - 移动机器人到指定位置" << std::endl;
        std::cout << "  rotate <robot_id> <theta>- 旋转机器人到指定角度" << std::endl;
        std::cout << "  goto <robot_id> <x> <y> <theta> - 机器人前往指定位置和角度" << std::endl;
        std::cout << "  stop <robot_id>          - 停止机器人" << std::endl;
        std::cout << "  missions                 - 列出活跃任务" << std::endl;
        std::cout << "  cancel <mission_id>      - 取消指定任务" << std::endl;
        std::cout << "  maps                     - 列出所有地图" << std::endl;
        std::cout << "  map <map_name>           - 显示地图详细信息" << std::endl;
        std::cout << "  quit/exit                - 退出程序" << std::endl;
    }
    
    void listRobots() {
        auto robots = controller_.getAllRobots();
        if (robots.empty()) {
            std::cout << "没有找到机器人或查询失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "\n机器人列表:" << std::endl;
        std::cout << std::setw(10) << "ID" << std::setw(15) << "名称" 
                  << std::setw(12) << "状态" << std::setw(10) << "X" 
                  << std::setw(10) << "Y" << std::setw(10) << "角度" 
                  << std::setw(10) << "电池%" << std::endl;
        std::cout << std::string(80, '-') << std::endl;
        
        for (const auto& robot : robots) {
            std::cout << std::setw(10) << robot.id 
                      << std::setw(15) << robot.name
                      << std::setw(12) << robot.status
                      << std::setw(10) << std::fixed << std::setprecision(2) << robot.x
                      << std::setw(10) << std::fixed << std::setprecision(2) << robot.y
                      << std::setw(10) << std::fixed << std::setprecision(2) << robot.theta
                      << std::setw(10) << std::fixed << std::setprecision(1) << robot.battery_level
                      << std::endl;
        }
    }
    
    void showRobotInfo(const std::string& robot_id) {
        if (robot_id.empty()) {
            std::cout << "请指定机器人ID" << std::endl;
            return;
        }
        
        auto robot = controller_.getRobotInfo(robot_id);
        if (!robot) {
            std::cout << "获取机器人信息失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "\n机器人信息:" << std::endl;
        std::cout << "  ID: " << robot->id << std::endl;
        std::cout << "  名称: " << robot->name << std::endl;
        std::cout << "  状态: " << robot->status << std::endl;
        std::cout << "  位置: (" << robot->x << ", " << robot->y << ")" << std::endl;
        std::cout << "  角度: " << robot->theta << " 弧度" << std::endl;
        std::cout << "  电池: " << robot->battery_level << "%" << std::endl;
        std::cout << "  地图: " << robot->map_name << std::endl;
    }
    
    void moveRobot(const std::string& robot_id, double x, double y) {
        if (robot_id.empty()) {
            std::cout << "请指定机器人ID" << std::endl;
            return;
        }
        
        if (controller_.moveRobot(robot_id, x, y)) {
            std::cout << "机器人 " << robot_id << " 开始移动到 (" << x << ", " << y << ")" << std::endl;
        } else {
            std::cout << "移动命令失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void rotateRobot(const std::string& robot_id, double theta) {
        if (robot_id.empty()) {
            std::cout << "请指定机器人ID" << std::endl;
            return;
        }
        
        if (controller_.rotateRobot(robot_id, theta)) {
            std::cout << "机器人 " << robot_id << " 开始旋转到 " << theta << " 弧度" << std::endl;
        } else {
            std::cout << "旋转命令失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void gotoPosition(const std::string& robot_id, double x, double y, double theta) {
        if (robot_id.empty()) {
            std::cout << "请指定机器人ID" << std::endl;
            return;
        }
        
        if (controller_.gotoPosition(robot_id, x, y, theta)) {
            std::cout << "机器人 " << robot_id << " 开始前往位置 (" << x << ", " << y 
                      << "), 角度 " << theta << " 弧度" << std::endl;
        } else {
            std::cout << "goto命令失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void stopRobot(const std::string& robot_id) {
        if (robot_id.empty()) {
            std::cout << "请指定机器人ID" << std::endl;
            return;
        }
        
        if (controller_.stopRobot(robot_id)) {
            std::cout << "机器人 " << robot_id << " 已停止" << std::endl;
        } else {
            std::cout << "停止命令失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void listMissions() {
        auto missions = controller_.getActiveMissions();
        if (missions.empty()) {
            std::cout << "没有活跃任务或查询失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "\n活跃任务列表:" << std::endl;
        std::cout << std::setw(15) << "任务ID" << std::setw(15) << "机器人ID" 
                  << std::setw(12) << "状态" << std::setw(15) << "类型" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        for (const auto& mission : missions) {
            std::cout << std::setw(15) << mission.id 
                      << std::setw(15) << mission.robot_id
                      << std::setw(12) << mission.status
                      << std::setw(15) << mission.mission_type << std::endl;
        }
    }
    
    void cancelMission(const std::string& mission_id) {
        if (mission_id.empty()) {
            std::cout << "请指定任务ID" << std::endl;
            return;
        }
        
        auto result = controller_.cancelMission(mission_id);
        if (result) {
            std::cout << "任务 " << mission_id << " 取消请求已发送，命令ID: " << result->id << std::endl;
        } else {
            std::cout << "取消任务失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void listMaps() {
        auto maps = controller_.getMapList();
        if (maps.empty()) {
            std::cout << "没有找到地图或查询失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "\n地图列表:" << std::endl;
        std::cout << std::setw(20) << "地图名称" << std::setw(30) << "描述" 
                  << std::setw(15) << "尺寸(宽x高)" << std::setw(12) << "分辨率" << std::endl;
        std::cout << std::string(80, '-') << std::endl;
        
        for (const auto& map : maps) {
            std::cout << std::setw(20) << map.name 
                      << std::setw(30) << map.description
                      << std::setw(15) << (std::to_string(map.width) + "x" + std::to_string(map.height))
                      << std::setw(12) << map.resolution << std::endl;
        }
    }
    
    void showMapInfo(const std::string& map_name) {
        if (map_name.empty()) {
            std::cout << "请指定地图名称" << std::endl;
            return;
        }
        
        auto map = controller_.getMapDetail(map_name);
        if (!map) {
            std::cout << "获取地图信息失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "\n地图详细信息:" << std::endl;
        std::cout << "  名称: " << map->name << std::endl;
        std::cout << "  描述: " << map->description << std::endl;
        std::cout << "  尺寸: " << map->width << " x " << map->height << std::endl;
        std::cout << "  分辨率: " << map->resolution << std::endl;
        std::cout << "  数据大小: " << map->data.size() << " 字节" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    std::string server_ip = "*************";  // 默认IP地址
    int port = 8858;
    
    // 解析命令行参数
    if (argc >= 2) {
        server_ip = argv[1];
    }
    if (argc >= 3) {
        port = std::stoi(argv[2]);
    }
    
    std::cout << "连接到AMR服务器: " << server_ip << ":" << port << std::endl;
    
    try {
        AMRControllerUI ui(server_ip, port);
        ui.run();
    } catch (const std::exception& e) {
        std::cerr << "程序启动失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
