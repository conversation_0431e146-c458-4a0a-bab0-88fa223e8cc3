#pragma once
#include <string>
#include <vector>
#include <nlohmann/json.hpp>

namespace GeekPlus {

// 机器人信息结构
struct Robot {
    std::string id;
    std::string name;
    std::string status;
    double x, y, theta;  // 位置和角度
    double battery_level;
    std::string map_name;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(Robot, id, name, status, x, y, theta, battery_level, map_name)
};

// 任务结构
struct Mission {
    std::string id;
    std::string uuid;
    std::string ref_uuid;
    std::string robot_id;
    std::string status;
    std::string mission_type;
    std::string start_time;
    std::string end_time;
    nlohmann::json mission_data;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(Mission, id, uuid, ref_uuid, robot_id, status, mission_type, start_time, end_time, mission_data)
};

// 任务模板结构
struct MissionTmpl {
    std::string template_id;
    std::string robot_id;
    std::string ref_uuid;
    nlohmann::json parameters;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(MissionTmpl, template_id, robot_id, ref_uuid, parameters)
};

// 任务命令结构
struct MissionCommand {
    std::string id;
    std::string mission_id;
    std::string command_type;  // "cancel", "pause", "resume"
    std::string status;
    std::string timestamp;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(MissionCommand, id, mission_id, command_type, status, timestamp)
};

// 地图信息结构
struct MapInfo {
    std::string name;
    std::string description;
    std::string created_time;
    double width, height;
    double resolution;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(MapInfo, name, description, created_time, width, height, resolution)
};

// 地图详细数据结构
struct MapDetail {
    std::string name;
    std::string description;
    double width, height;
    double resolution;
    std::vector<uint8_t> data;  // 地图数据
    
    // 自定义序列化
    void to_json(nlohmann::json& j) const {
        j = nlohmann::json{{"name", name}, {"description", description}, 
                          {"width", width}, {"height", height}, {"resolution", resolution}};
    }
    
    void from_json(const nlohmann::json& j) {
        j.at("name").get_to(name);
        j.at("description").get_to(description);
        j.at("width").get_to(width);
        j.at("height").get_to(height);
        j.at("resolution").get_to(resolution);
    }
};

// TSP请求结构
struct TspRequest {
    std::vector<std::pair<double, double>> points;
    std::string start_point;
    std::string algorithm;
    
    void to_json(nlohmann::json& j) const {
        j = nlohmann::json{{"points", points}, {"start_point", start_point}, {"algorithm", algorithm}};
    }
};

// TSP响应结构
struct TspResponse {
    std::vector<int> route;
    double total_distance;
    std::string status;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(TspResponse, route, total_distance, status)
};

// 机器人指令结构
struct RobotCommand {
    std::string robot_id;
    std::string command_type;  // "move", "rotate", "stop", "goto"
    nlohmann::json parameters;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(RobotCommand, robot_id, command_type, parameters)
};

// 机器人指令响应结构
struct RobotCommandResp {
    std::string command_id;
    std::string status;
    std::string message;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(RobotCommandResp, command_id, status, message)
};

// API错误响应结构
struct ApiError {
    int code;
    std::string msg;
    
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(ApiError, code, msg)
};

} // namespace GeekPlus
