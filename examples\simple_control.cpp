#include "../include/AMRController.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace GeekPlus;

int main() {
    try {
        // 创建AMR控制器实例
        AMRController controller("192.168.1.100", 8858);
        
        std::cout << "=== 迦智AMR机器人简单控制示例 ===" << std::endl;
        
        // 1. 获取所有机器人信息
        std::cout << "\n1. 获取机器人列表..." << std::endl;
        auto robots = controller.getAllRobots();
        
        if (robots.empty()) {
            std::cout << "错误: 未找到机器人或连接失败" << std::endl;
            std::cout << "错误信息: " << controller.getLastError() << std::endl;
            return 1;
        }
        
        std::cout << "找到 " << robots.size() << " 个机器人:" << std::endl;
        for (const auto& robot : robots) {
            std::cout << "  - " << robot.id << " (" << robot.name << ") - " 
                      << robot.status << std::endl;
        }
        
        // 使用第一个机器人进行演示
        const std::string robot_id = robots[0].id;
        std::cout << "\n使用机器人: " << robot_id << std::endl;
        
        // 2. 获取机器人详细信息
        std::cout << "\n2. 获取机器人详细信息..." << std::endl;
        auto robot_info = controller.getRobotInfo(robot_id);
        if (robot_info) {
            std::cout << "  位置: (" << robot_info->x << ", " << robot_info->y << ")" << std::endl;
            std::cout << "  角度: " << robot_info->theta << " 弧度" << std::endl;
            std::cout << "  电池: " << robot_info->battery_level << "%" << std::endl;
            std::cout << "  状态: " << robot_info->status << std::endl;
        }
        
        // 3. 停止机器人（安全起见）
        std::cout << "\n3. 停止机器人..." << std::endl;
        if (controller.stopRobot(robot_id)) {
            std::cout << "  ✓ 机器人已停止" << std::endl;
        } else {
            std::cout << "  ✗ 停止失败: " << controller.getLastError() << std::endl;
        }
        
        // 等待2秒
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 4. 移动机器人到新位置
        std::cout << "\n4. 移动机器人..." << std::endl;
        double target_x = robot_info ? robot_info->x + 1.0 : 1.0;
        double target_y = robot_info ? robot_info->y + 1.0 : 1.0;
        
        if (controller.moveRobot(robot_id, target_x, target_y)) {
            std::cout << "  ✓ 开始移动到 (" << target_x << ", " << target_y << ")" << std::endl;
        } else {
            std::cout << "  ✗ 移动失败: " << controller.getLastError() << std::endl;
        }
        
        // 等待5秒让机器人移动
        std::cout << "  等待机器人移动..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 5. 旋转机器人
        std::cout << "\n5. 旋转机器人..." << std::endl;
        if (controller.rotateRobot(robot_id, 1.57)) {  // 90度 (π/2弧度)
            std::cout << "  ✓ 开始旋转到90度" << std::endl;
        } else {
            std::cout << "  ✗ 旋转失败: " << controller.getLastError() << std::endl;
        }
        
        // 等待3秒让机器人旋转
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 6. 使用goto命令前往指定位置
        std::cout << "\n6. 前往指定位置..." << std::endl;
        double goto_x = robot_info ? robot_info->x : 0.0;
        double goto_y = robot_info ? robot_info->y : 0.0;
        double goto_theta = 0.0;
        
        if (controller.gotoPosition(robot_id, goto_x, goto_y, goto_theta)) {
            std::cout << "  ✓ 开始前往原始位置 (" << goto_x << ", " << goto_y 
                      << "), 角度 " << goto_theta << std::endl;
        } else {
            std::cout << "  ✗ goto失败: " << controller.getLastError() << std::endl;
        }
        
        // 等待机器人到达
        std::cout << "  等待机器人到达目标位置..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(8));
        
        // 7. 最终停止
        std::cout << "\n7. 最终停止机器人..." << std::endl;
        if (controller.stopRobot(robot_id)) {
            std::cout << "  ✓ 机器人已停止" << std::endl;
        } else {
            std::cout << "  ✗ 停止失败: " << controller.getLastError() << std::endl;
        }
        
        // 8. 获取任务信息
        std::cout << "\n8. 查询活跃任务..." << std::endl;
        auto missions = controller.getActiveMissions();
        std::cout << "  当前活跃任务数量: " << missions.size() << std::endl;
        
        // 9. 获取地图信息
        std::cout << "\n9. 查询地图信息..." << std::endl;
        auto maps = controller.getMapList();
        std::cout << "  可用地图数量: " << maps.size() << std::endl;
        if (!maps.empty()) {
            std::cout << "  第一个地图: " << maps[0].name << std::endl;
        }
        
        std::cout << "\n=== 演示完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "程序执行失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
