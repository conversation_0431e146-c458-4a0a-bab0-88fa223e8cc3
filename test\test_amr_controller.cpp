#include "../include/AMRController.h"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

using namespace GeekPlus;

class AMRControllerTest {
public:
    AMRControllerTest(const std::string& server_ip, int port = 8858) 
        : controller_(server_ip, port) {
        std::cout << "=== 迦智AMR机器人控制程序测试 ===" << std::endl;
        std::cout << "服务器地址: " << server_ip << ":" << port << std::endl;
    }
    
    void runAllTests() {
        std::cout << "\n开始运行测试..." << std::endl;
        
        // 基础连接测试
        testConnection();
        
        // 机器人信息测试
        testRobotInfo();
        
        // 任务管理测试
        testMissionManagement();
        
        // 地图信息测试
        testMapInfo();
        
        // 机器人控制测试
        testRobotControl();
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
    }

private:
    AMRController controller_;
    
    void testConnection() {
        std::cout << "\n[测试] 连接测试..." << std::endl;
        
        // 尝试获取所有机器人信息来测试连接
        auto robots = controller_.getAllRobots();
        
        if (robots.empty()) {
            std::cout << "  警告: 未获取到机器人信息，可能的原因:" << std::endl;
            std::cout << "    - 服务器未启动" << std::endl;
            std::cout << "    - 网络连接问题" << std::endl;
            std::cout << "    - 服务器地址或端口错误" << std::endl;
            std::cout << "  错误信息: " << controller_.getLastError() << std::endl;
        } else {
            std::cout << "  ✓ 连接成功，找到 " << robots.size() << " 个机器人" << std::endl;
        }
    }
    
    void testRobotInfo() {
        std::cout << "\n[测试] 机器人信息查询..." << std::endl;
        
        // 获取所有机器人
        auto robots = controller_.getAllRobots();
        
        if (robots.empty()) {
            std::cout << "  跳过: 没有可用的机器人" << std::endl;
            return;
        }
        
        std::cout << "  ✓ 成功获取 " << robots.size() << " 个机器人信息" << std::endl;
        
        // 测试获取单个机器人信息
        const auto& first_robot = robots[0];
        auto robot_detail = controller_.getRobotInfo(first_robot.id);
        
        if (robot_detail) {
            std::cout << "  ✓ 成功获取机器人 " << first_robot.id << " 的详细信息" << std::endl;
            std::cout << "    位置: (" << robot_detail->x << ", " << robot_detail->y << ")" << std::endl;
            std::cout << "    状态: " << robot_detail->status << std::endl;
            std::cout << "    电池: " << robot_detail->battery_level << "%" << std::endl;
        } else {
            std::cout << "  ✗ 获取机器人详细信息失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void testMissionManagement() {
        std::cout << "\n[测试] 任务管理..." << std::endl;
        
        // 获取活跃任务
        auto missions = controller_.getActiveMissions();
        std::cout << "  ✓ 当前活跃任务数量: " << missions.size() << std::endl;
        
        if (!missions.empty()) {
            // 显示第一个任务的信息
            const auto& mission = missions[0];
            std::cout << "    任务ID: " << mission.id << std::endl;
            std::cout << "    机器人ID: " << mission.robot_id << std::endl;
            std::cout << "    状态: " << mission.status << std::endl;
            std::cout << "    类型: " << mission.mission_type << std::endl;
        }
        
        // 注意: 这里不创建实际任务，避免影响生产环境
        std::cout << "  注意: 跳过任务创建测试以避免影响生产环境" << std::endl;
    }
    
    void testMapInfo() {
        std::cout << "\n[测试] 地图信息查询..." << std::endl;
        
        // 获取地图列表
        auto maps = controller_.getMapList();
        
        if (maps.empty()) {
            std::cout << "  警告: 未找到地图信息" << std::endl;
            std::cout << "  错误信息: " << controller_.getLastError() << std::endl;
            return;
        }
        
        std::cout << "  ✓ 成功获取 " << maps.size() << " 个地图" << std::endl;
        
        // 获取第一个地图的详细信息
        const auto& first_map = maps[0];
        std::cout << "    地图名称: " << first_map.name << std::endl;
        std::cout << "    描述: " << first_map.description << std::endl;
        std::cout << "    尺寸: " << first_map.width << " x " << first_map.height << std::endl;
        
        // 获取地图详细数据
        auto map_detail = controller_.getMapDetail(first_map.name);
        if (map_detail) {
            std::cout << "  ✓ 成功获取地图 " << first_map.name << " 的详细数据" << std::endl;
            std::cout << "    数据大小: " << map_detail->data.size() << " 字节" << std::endl;
        } else {
            std::cout << "  ✗ 获取地图详细数据失败: " << controller_.getLastError() << std::endl;
        }
    }
    
    void testRobotControl() {
        std::cout << "\n[测试] 机器人控制..." << std::endl;
        
        // 获取第一个可用的机器人
        auto robots = controller_.getAllRobots();
        if (robots.empty()) {
            std::cout << "  跳过: 没有可用的机器人进行控制测试" << std::endl;
            return;
        }
        
        const std::string& robot_id = robots[0].id;
        std::cout << "  使用机器人: " << robot_id << std::endl;
        
        // 注意: 在实际测试中，这些命令会真正控制机器人
        // 在生产环境中应该谨慎使用
        std::cout << "  注意: 以下为模拟测试，不会发送实际控制命令" << std::endl;
        
        // 模拟测试各种控制命令
        std::cout << "  [模拟] 测试停止命令..." << std::endl;
        // bool stop_result = controller_.stopRobot(robot_id);
        
        std::cout << "  [模拟] 测试移动命令..." << std::endl;
        // bool move_result = controller_.moveRobot(robot_id, 1.0, 1.0);
        
        std::cout << "  [模拟] 测试旋转命令..." << std::endl;
        // bool rotate_result = controller_.rotateRobot(robot_id, 0.5);
        
        std::cout << "  [模拟] 测试goto命令..." << std::endl;
        // bool goto_result = controller_.gotoPosition(robot_id, 2.0, 2.0, 0.0);
        
        std::cout << "  ✓ 控制命令接口测试完成" << std::endl;
        std::cout << "  警告: 实际控制命令已被注释以保护机器人安全" << std::endl;
    }
    
    // 实际控制测试（需要谨慎使用）
    void testRealRobotControl(const std::string& robot_id) {
        std::cout << "\n[实际测试] 机器人控制 - 机器人ID: " << robot_id << std::endl;
        std::cout << "警告: 这将发送真实的控制命令给机器人!" << std::endl;
        
        // 首先停止机器人确保安全
        std::cout << "  发送停止命令..." << std::endl;
        bool stop_result = controller_.stopRobot(robot_id);
        if (stop_result) {
            std::cout << "  ✓ 停止命令成功" << std::endl;
        } else {
            std::cout << "  ✗ 停止命令失败: " << controller_.getLastError() << std::endl;
            return;
        }
        
        // 等待一段时间
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 测试小幅度移动
        std::cout << "  发送小幅度移动命令..." << std::endl;
        bool move_result = controller_.moveRobot(robot_id, 0.1, 0.1);
        if (move_result) {
            std::cout << "  ✓ 移动命令成功" << std::endl;
        } else {
            std::cout << "  ✗ 移动命令失败: " << controller_.getLastError() << std::endl;
        }
        
        // 等待移动完成
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 再次停止
        std::cout << "  发送最终停止命令..." << std::endl;
        controller_.stopRobot(robot_id);
    }
};

int main(int argc, char* argv[]) {
    std::string server_ip = "*************";  // 默认IP地址
    int port = 8858;
    
    // 解析命令行参数
    if (argc >= 2) {
        server_ip = argv[1];
    }
    if (argc >= 3) {
        port = std::stoi(argv[2]);
    }
    
    try {
        AMRControllerTest test(server_ip, port);
        test.runAllTests();
        
        // 询问是否进行实际控制测试
        std::cout << "\n是否进行实际机器人控制测试? (y/N): ";
        std::string input;
        std::getline(std::cin, input);
        
        if (input == "y" || input == "Y") {
            std::cout << "请输入要测试的机器人ID: ";
            std::string robot_id;
            std::getline(std::cin, robot_id);
            
            if (!robot_id.empty()) {
                test.testRealRobotControl(robot_id);
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "测试程序启动失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
