#include "DataStructures.h"

namespace GeekPlus {

// 这个文件主要用于实现一些复杂的序列化/反序列化逻辑
// 目前大部分结构体使用NLOHMANN_DEFINE_TYPE_INTRUSIVE宏自动生成
// 如果需要自定义序列化逻辑，可以在这里实现

// TspRequest的序列化实现
void TspRequest::to_json(nlohmann::json& j) const {
    j = nlohmann::json{
        {"points", points}, 
        {"start_point", start_point}, 
        {"algorithm", algorithm}
    };
}

// MapDetail的序列化实现已在头文件中定义

} // namespace GeekPlus
