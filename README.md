# 迦智AMR机器人控制程序

这是一个基于C++开发的迦智科技AMR机器人上位机控制程序，通过HTTP RESTful API与机器人进行通信。

## 功能特性

- **机器人状态查询**: 获取单个或所有机器人的状态信息
- **机器人控制**: 支持移动、旋转、停止、前往指定位置等基本控制命令
- **任务管理**: 创建、查询、取消任务
- **地图管理**: 查询地图信息和详细数据
- **TSP求解**: 支持旅行商问题求解
- **命令行界面**: 提供友好的交互式命令行界面

## 系统要求

- C++17 或更高版本
- CMake 3.10 或更高版本
- libcurl 开发库
- nlohmann/json 库

## 依赖安装

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential cmake pkg-config
sudo apt-get install libcurl4-openssl-dev nlohmann-json3-dev
```

### CentOS/RHEL
```bash
sudo yum install gcc-c++ cmake pkg-config
sudo yum install libcurl-devel
# nlohmann/json 可能需要从源码编译安装
```

### 从源码安装nlohmann/json (如果包管理器中没有)
```bash
git clone https://github.com/nlohmann/json.git
cd json
mkdir build && cd build
cmake .. -DJSON_BuildTests=OFF
make -j$(nproc)
sudo make install
```

## 编译

1. 克隆或下载项目代码
2. 运行编译脚本:
```bash
chmod +x build.sh
./build.sh
```

或者手动编译:
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 使用方法

### 启动程序
```bash
# 使用默认IP地址 (*************:8858)
./build/GeekPlusAMRController

# 指定服务器IP和端口
./build/GeekPlusAMRController ************* 8858
```

### 可用命令

程序启动后，可以使用以下命令:

#### 基本命令
- `help` - 显示帮助信息
- `quit` 或 `exit` - 退出程序

#### 机器人管理
- `robots` - 列出所有机器人
- `robot <id>` - 显示指定机器人详细信息

#### 机器人控制
- `move <robot_id> <x> <y>` - 移动机器人到指定位置
- `rotate <robot_id> <theta>` - 旋转机器人到指定角度(弧度)
- `goto <robot_id> <x> <y> <theta>` - 机器人前往指定位置和角度
- `stop <robot_id>` - 停止机器人

#### 任务管理
- `missions` - 列出所有活跃任务
- `cancel <mission_id>` - 取消指定任务

#### 地图管理
- `maps` - 列出所有地图
- `map <map_name>` - 显示地图详细信息

### 使用示例

```bash
> robots                           # 查看所有机器人
> robot robot001                   # 查看robot001的详细信息
> move robot001 10.5 20.3          # 移动robot001到坐标(10.5, 20.3)
> rotate robot001 1.57             # 旋转robot001到90度(π/2弧度)
> goto robot001 15.0 25.0 0.0      # robot001前往(15.0, 25.0)位置，角度为0
> stop robot001                    # 停止robot001
> missions                         # 查看活跃任务
> maps                             # 查看所有地图
```

## API接口说明

程序基于迦智科技标准API接口文档实现，支持以下接口:

- **机器人信息**: GET /api/v1/robots/{id}, GET /api/v1/robots
- **任务管理**: POST /api/v1/missions, PUT /api/v1/missions, GET /api/v1/missions
- **任务控制**: POST /api/v1/mscmds, GET /api/v1/mscmds/{id}
- **地图管理**: GET /api/v1/maps, GET /api/v1/maps/{mapname}
- **TSP求解**: POST /api/v1/tsp
- **机器人控制**: POST /api/v1/robcmd

## 项目结构

```
.
├── CMakeLists.txt          # CMake构建配置
├── build.sh               # 编译脚本
├── README.md              # 说明文档
├── include/               # 头文件目录
│   ├── AMRController.h    # AMR控制器类
│   ├── HttpClient.h       # HTTP客户端类
│   └── DataStructures.h   # 数据结构定义
└── src/                   # 源文件目录
    ├── main.cpp           # 主程序和用户界面
    ├── AMRController.cpp  # AMR控制器实现
    ├── HttpClient.cpp     # HTTP客户端实现
    └── DataStructures.cpp # 数据结构实现
```

## 错误处理

程序包含完善的错误处理机制:
- HTTP请求错误会显示详细的错误信息
- API错误会解析并显示服务器返回的错误代码和消息
- 网络连接问题会给出相应的提示

## 注意事项

1. 确保AMR机器人服务器正在运行并且网络连接正常
2. 默认端口为8858，如果服务器使用不同端口请在启动时指定
3. 坐标系和角度单位请参考迦智科技的API文档
4. 某些命令可能需要机器人处于特定状态才能执行

## 许可证

本项目仅供学习和研究使用。
