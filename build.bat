@echo off
echo === 迦智AMR机器人控制程序编译脚本 (Windows) ===

REM 检查cmake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到cmake，请先安装cmake
    echo 下载地址: https://cmake.org/download/
    pause
    exit /b 1
)

REM 检查Visual Studio或MinGW
where cl >nul 2>&1
if errorlevel 1 (
    where g++ >nul 2>&1
    if errorlevel 1 (
        echo 错误: 未找到C++编译器
        echo 请安装Visual Studio或MinGW
        pause
        exit /b 1
    ) else (
        echo 使用MinGW编译器
        set GENERATOR="MinGW Makefiles"
    )
) else (
    echo 使用Visual Studio编译器
    set GENERATOR="Visual Studio 16 2019"
)

echo.
echo 注意: 请确保已安装以下依赖:
echo   - libcurl 开发库
echo   - nlohmann/json 库
echo.
echo 对于Windows，建议使用vcpkg安装依赖:
echo   vcpkg install curl nlohmann-json
echo.

REM 创建构建目录
echo 创建构建目录...
if not exist build mkdir build
cd build

REM 运行cmake
echo 配置项目...
cmake .. -G %GENERATOR% -DCMAKE_BUILD_TYPE=Release

if errorlevel 1 (
    echo 错误: cmake配置失败
    echo 请检查依赖库是否正确安装
    pause
    exit /b 1
)

REM 编译
echo 编译项目...
cmake --build . --config Release

if errorlevel 1 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件位置: build\Release\GeekPlusAMRController.exe (或 build\GeekPlusAMRController.exe)
echo 测试程序位置: build\Release\GeekPlusAMRController_test.exe (或 build\GeekPlusAMRController_test.exe)
echo.
echo 使用方法:
echo   GeekPlusAMRController.exe [服务器IP] [端口]
echo   例如: GeekPlusAMRController.exe ************* 8858
echo.
pause
