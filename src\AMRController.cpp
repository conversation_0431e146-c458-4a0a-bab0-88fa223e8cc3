#include "AMRController.h"
#include <iostream>
#include <sstream>

namespace GeekPlus {

AMRController::AMRController(const std::string& server_ip, int port) {
    std::ostringstream oss;
    oss << "http://" << server_ip << ":" << port;
    http_client_ = std::make_unique<HttpClient>(oss.str());
}

template<typename T>
std::optional<T> AMRController::handleResponse(const HttpResponse& response) {
    if (!response.success) {
        setError("HTTP request failed: " + response.error_message);
        return std::nullopt;
    }
    
    try {
        if (response.status_code >= 400) {
            // 尝试解析错误信息
            auto error_json = nlohmann::json::parse(response.body);
            ApiError error = error_json.get<ApiError>();
            setError("API Error " + std::to_string(error.code) + ": " + error.msg);
            return std::nullopt;
        }
        
        auto json_response = nlohmann::json::parse(response.body);
        T result = json_response.get<T>();
        return result;
    } catch (const nlohmann::json::exception& e) {
        setError("JSON parsing error: " + std::string(e.what()));
        return std::nullopt;
    }
}

template<typename T>
std::vector<T> AMRController::handleVectorResponse(const HttpResponse& response) {
    std::vector<T> result;
    
    if (!response.success) {
        setError("HTTP request failed: " + response.error_message);
        return result;
    }
    
    try {
        if (response.status_code >= 400) {
            auto error_json = nlohmann::json::parse(response.body);
            ApiError error = error_json.get<ApiError>();
            setError("API Error " + std::to_string(error.code) + ": " + error.msg);
            return result;
        }
        
        auto json_response = nlohmann::json::parse(response.body);
        result = json_response.get<std::vector<T>>();
    } catch (const nlohmann::json::exception& e) {
        setError("JSON parsing error: " + std::string(e.what()));
    }
    
    return result;
}

std::optional<Robot> AMRController::getRobotInfo(const std::string& robot_id) {
    auto response = http_client_->get("/api/v1/robots/" + robot_id);
    return handleResponse<Robot>(response);
}

std::vector<Robot> AMRController::getAllRobots() {
    auto response = http_client_->get("/api/v1/robots");
    return handleVectorResponse<Robot>(response);
}

std::optional<Mission> AMRController::createMission(const Mission& mission) {
    nlohmann::json mission_json = mission;
    auto response = http_client_->post("/api/v1/missions", mission_json);
    return handleResponse<Mission>(response);
}

std::optional<Mission> AMRController::createMissionWithUuid(const Mission& mission) {
    nlohmann::json mission_json = mission;
    auto response = http_client_->put("/api/v1/missions", mission_json);
    return handleResponse<Mission>(response);
}

std::optional<Mission> AMRController::createMissionByTemplate(const MissionTmpl& mission_tmpl) {
    nlohmann::json tmpl_json = mission_tmpl;
    auto response = http_client_->post("/api/v1/missions/bytmpl", tmpl_json);
    return handleResponse<Mission>(response);
}

std::vector<Mission> AMRController::getActiveMissions() {
    auto response = http_client_->get("/api/v1/missions");
    return handleVectorResponse<Mission>(response);
}

std::optional<Mission> AMRController::getMissionById(const std::string& mission_id) {
    auto response = http_client_->get("/api/v1/missions/" + mission_id);
    return handleResponse<Mission>(response);
}

std::optional<Mission> AMRController::getMissionByUuid(const std::string& uuid) {
    auto response = http_client_->get("/api/v1/uuid/missions/" + uuid);
    return handleResponse<Mission>(response);
}

std::optional<MissionCommand> AMRController::cancelMission(const std::string& mission_id) {
    MissionCommand cmd;
    cmd.mission_id = mission_id;
    cmd.command_type = "cancel";
    
    nlohmann::json cmd_json = cmd;
    auto response = http_client_->post("/api/v1/mscmds", cmd_json);
    return handleResponse<MissionCommand>(response);
}

std::optional<MissionCommand> AMRController::getMissionCommandResult(const std::string& command_id) {
    auto response = http_client_->get("/api/v1/mscmds/" + command_id);
    return handleResponse<MissionCommand>(response);
}

std::vector<MapInfo> AMRController::getMapList() {
    auto response = http_client_->get("/api/v1/maps");
    return handleVectorResponse<MapInfo>(response);
}

std::optional<MapDetail> AMRController::getMapDetail(const std::string& map_name) {
    auto response = http_client_->get("/api/v1/maps/" + map_name);
    return handleResponse<MapDetail>(response);
}

std::optional<TspResponse> AMRController::solveTsp(const TspRequest& request) {
    nlohmann::json req_json;
    request.to_json(req_json);
    auto response = http_client_->post("/api/v1/tsp", req_json);
    return handleResponse<TspResponse>(response);
}

std::optional<RobotCommandResp> AMRController::sendRobotCommand(const RobotCommand& command) {
    nlohmann::json cmd_json = command;
    auto response = http_client_->post("/api/v1/robcmd", cmd_json);
    return handleResponse<RobotCommandResp>(response);
}

void AMRController::setTimeout(long timeout_seconds) {
    http_client_->setTimeout(timeout_seconds);
}

std::string AMRController::getLastError() const {
    return last_error_;
}

// 便捷控制方法实现
bool AMRController::moveRobot(const std::string& robot_id, double x, double y) {
    RobotCommand cmd;
    cmd.robot_id = robot_id;
    cmd.command_type = "move";
    cmd.parameters = nlohmann::json{{"x", x}, {"y", y}};

    auto result = sendRobotCommand(cmd);
    return result.has_value() && result->status == "success";
}

bool AMRController::rotateRobot(const std::string& robot_id, double theta) {
    RobotCommand cmd;
    cmd.robot_id = robot_id;
    cmd.command_type = "rotate";
    cmd.parameters = nlohmann::json{{"theta", theta}};

    auto result = sendRobotCommand(cmd);
    return result.has_value() && result->status == "success";
}

bool AMRController::stopRobot(const std::string& robot_id) {
    RobotCommand cmd;
    cmd.robot_id = robot_id;
    cmd.command_type = "stop";
    cmd.parameters = nlohmann::json{};

    auto result = sendRobotCommand(cmd);
    return result.has_value() && result->status == "success";
}

bool AMRController::gotoPosition(const std::string& robot_id, double x, double y, double theta) {
    RobotCommand cmd;
    cmd.robot_id = robot_id;
    cmd.command_type = "goto";
    cmd.parameters = nlohmann::json{{"x", x}, {"y", y}, {"theta", theta}};

    auto result = sendRobotCommand(cmd);
    return result.has_value() && result->status == "success";
}

void AMRController::setError(const std::string& error) {
    last_error_ = error;
    std::cerr << "AMRController Error: " << error << std::endl;
}

} // namespace GeekPlus
