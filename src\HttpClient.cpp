#include "HttpClient.h"
#include <iostream>
#include <sstream>

namespace GeekPlus {

HttpClient::HttpClient(const std::string& base_url) 
    : curl_(nullptr), base_url_(base_url), timeout_(30), connect_timeout_(10) {
    if (!initCurl()) {
        throw std::runtime_error("Failed to initialize CURL");
    }
}

HttpClient::~HttpClient() {
    cleanupCurl();
}

bool HttpClient::initCurl() {
    curl_global_init(CURL_GLOBAL_DEFAULT);
    curl_ = curl_easy_init();
    return curl_ != nullptr;
}

void HttpClient::cleanupCurl() {
    if (curl_) {
        curl_easy_cleanup(curl_);
        curl_ = nullptr;
    }
    curl_global_cleanup();
}

size_t HttpClient::writeCallback(void* contents, size_t size, size_t nmemb, std::string* data) {
    size_t total_size = size * nmemb;
    data->append(static_cast<char*>(contents), total_size);
    return total_size;
}

HttpResponse HttpClient::get(const std::string& endpoint, const std::string& params) {
    std::string url = base_url_ + endpoint;
    if (!params.empty()) {
        url += "?" + params;
    }
    return performRequest(url, "GET");
}

HttpResponse HttpClient::post(const std::string& endpoint, const nlohmann::json& data) {
    std::string url = base_url_ + endpoint;
    std::string json_data = data.dump();
    return performRequest(url, "POST", json_data);
}

HttpResponse HttpClient::put(const std::string& endpoint, const nlohmann::json& data) {
    std::string url = base_url_ + endpoint;
    std::string json_data = data.dump();
    return performRequest(url, "PUT", json_data);
}

HttpResponse HttpClient::performRequest(const std::string& url, const std::string& method, 
                                       const std::string& data) {
    HttpResponse response;
    response.success = false;
    
    if (!curl_) {
        response.error_message = "CURL not initialized";
        return response;
    }
    
    // 重置curl选项
    curl_easy_reset(curl_);
    
    // 设置URL
    curl_easy_setopt(curl_, CURLOPT_URL, url.c_str());
    
    // 设置写回调
    std::string response_body;
    curl_easy_setopt(curl_, CURLOPT_WRITEFUNCTION, writeCallback);
    curl_easy_setopt(curl_, CURLOPT_WRITEDATA, &response_body);
    
    // 设置超时
    curl_easy_setopt(curl_, CURLOPT_TIMEOUT, timeout_);
    curl_easy_setopt(curl_, CURLOPT_CONNECTTIMEOUT, connect_timeout_);
    
    // 设置HTTP头
    struct curl_slist* headers = nullptr;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    headers = curl_slist_append(headers, "Accept: application/json");
    curl_easy_setopt(curl_, CURLOPT_HTTPHEADER, headers);
    
    // 设置HTTP方法和数据
    if (method == "POST") {
        curl_easy_setopt(curl_, CURLOPT_POST, 1L);
        if (!data.empty()) {
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDS, data.c_str());
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDSIZE, data.length());
        }
    } else if (method == "PUT") {
        curl_easy_setopt(curl_, CURLOPT_CUSTOMREQUEST, "PUT");
        if (!data.empty()) {
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDS, data.c_str());
            curl_easy_setopt(curl_, CURLOPT_POSTFIELDSIZE, data.length());
        }
    } else if (method == "GET") {
        curl_easy_setopt(curl_, CURLOPT_HTTPGET, 1L);
    }
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl_);
    
    // 清理headers
    curl_slist_free_all(headers);
    
    if (res != CURLE_OK) {
        response.error_message = curl_easy_strerror(res);
        return response;
    }
    
    // 获取HTTP状态码
    curl_easy_getinfo(curl_, CURLINFO_RESPONSE_CODE, &response.status_code);
    response.body = response_body;
    response.success = (response.status_code >= 200 && response.status_code < 300);
    
    if (!response.success) {
        std::ostringstream oss;
        oss << "HTTP Error " << response.status_code;
        response.error_message = oss.str();
    }
    
    return response;
}

void HttpClient::setTimeout(long timeout_seconds) {
    timeout_ = timeout_seconds;
}

void HttpClient::setConnectTimeout(long timeout_seconds) {
    connect_timeout_ = timeout_seconds;
}

} // namespace GeekPlus
