#pragma once
#include "HttpClient.h"
#include "DataStructures.h"
#include <memory>
#include <vector>
#include <optional>

namespace GeekPlus {

class AMRController {
public:
    explicit AMRController(const std::string& server_ip, int port = 8858);
    ~AMRController() = default;
    
    // 禁用拷贝构造和赋值
    AMRController(const AMRController&) = delete;
    AMRController& operator=(const AMRController&) = delete;
    
    // 机器人信息查询
    std::optional<Robot> getRobotInfo(const std::string& robot_id);
    std::vector<Robot> getAllRobots();
    
    // 任务管理
    std::optional<Mission> createMission(const Mission& mission);
    std::optional<Mission> createMissionWithUuid(const Mission& mission);
    std::optional<Mission> createMissionByTemplate(const MissionTmpl& mission_tmpl);
    std::vector<Mission> getActiveMissions();
    std::optional<Mission> getMissionById(const std::string& mission_id);
    std::optional<Mission> getMissionByUuid(const std::string& uuid);
    
    // 任务控制
    std::optional<MissionCommand> cancelMission(const std::string& mission_id);
    std::optional<MissionCommand> getMissionCommandResult(const std::string& command_id);
    
    // 地图管理
    std::vector<MapInfo> getMapList();
    std::optional<MapDetail> getMapDetail(const std::string& map_name);
    
    // TSP求解
    std::optional<TspResponse> solveTsp(const TspRequest& request);
    
    // 机器人直接控制
    std::optional<RobotCommandResp> sendRobotCommand(const RobotCommand& command);
    
    // 便捷控制方法
    bool moveRobot(const std::string& robot_id, double x, double y);
    bool rotateRobot(const std::string& robot_id, double theta);
    bool stopRobot(const std::string& robot_id);
    bool gotoPosition(const std::string& robot_id, double x, double y, double theta);
    
    // 设置超时时间
    void setTimeout(long timeout_seconds);
    
    // 获取最后的错误信息
    std::string getLastError() const;

private:
    std::unique_ptr<HttpClient> http_client_;
    std::string last_error_;
    
    // 处理HTTP响应
    template<typename T>
    std::optional<T> handleResponse(const HttpResponse& response);
    
    // 处理HTTP响应（向量类型）
    template<typename T>
    std::vector<T> handleVectorResponse(const HttpResponse& response);
    
    // 设置错误信息
    void setError(const std::string& error);
};

} // namespace GeekPlus
