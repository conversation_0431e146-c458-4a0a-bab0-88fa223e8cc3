#!/bin/bash

# 迦智AMR机器人控制程序编译脚本

echo "=== 迦智AMR机器人控制程序编译脚本 ==="

# 检查依赖
echo "检查依赖..."

# 检查cmake
if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到cmake，请先安装cmake"
    exit 1
fi

# 检查pkg-config
if ! command -v pkg-config &> /dev/null; then
    echo "错误: 未找到pkg-config，请先安装pkg-config"
    exit 1
fi

# 检查libcurl
if ! pkg-config --exists libcurl; then
    echo "错误: 未找到libcurl开发库，请安装libcurl-dev"
    echo "Ubuntu/Debian: sudo apt-get install libcurl4-openssl-dev"
    echo "CentOS/RHEL: sudo yum install libcurl-devel"
    exit 1
fi

# 检查nlohmann/json
if ! pkg-config --exists nlohmann_json; then
    echo "警告: 未找到nlohmann/json库，尝试使用系统包管理器安装"
    echo "Ubuntu/Debian: sudo apt-get install nlohmann-json3-dev"
    echo "或者从源码编译安装"
fi

# 创建构建目录
echo "创建构建目录..."
mkdir -p build
cd build

# 运行cmake
echo "配置项目..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "错误: cmake配置失败"
    exit 1
fi

# 编译
echo "编译项目..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "编译成功！"
echo "可执行文件位置: build/GeekPlusAMRController"
echo ""
echo "使用方法:"
echo "  ./GeekPlusAMRController [服务器IP] [端口]"
echo "  例如: ./GeekPlusAMRController ************* 8858"
