#pragma once
#include <string>
#include <memory>
#include <curl/curl.h>
#include <nlohmann/json.hpp>

namespace GeekPlus {

struct HttpResponse {
    long status_code;
    std::string body;
    bool success;
    std::string error_message;
};

class HttpClient {
public:
    HttpClient(const std::string& base_url);
    ~HttpClient();
    
    // 禁用拷贝构造和赋值
    HttpClient(const HttpClient&) = delete;
    HttpClient& operator=(const HttpClient&) = delete;
    
    // GET请求
    HttpResponse get(const std::string& endpoint, const std::string& params = "");
    
    // POST请求
    HttpResponse post(const std::string& endpoint, const nlohmann::json& data);
    
    // PUT请求
    HttpResponse put(const std::string& endpoint, const nlohmann::json& data);
    
    // 设置超时时间
    void setTimeout(long timeout_seconds);
    
    // 设置连接超时时间
    void setConnectTimeout(long timeout_seconds);

private:
    CURL* curl_;
    std::string base_url_;
    long timeout_;
    long connect_timeout_;
    
    // 初始化curl
    bool initCurl();
    
    // 清理curl
    void cleanupCurl();
    
    // 执行HTTP请求
    HttpResponse performRequest(const std::string& url, const std::string& method, 
                               const std::string& data = "");
    
    // curl写回调函数
    static size_t writeCallback(void* contents, size_t size, size_t nmemb, std::string* data);
};

} // namespace GeekPlus
